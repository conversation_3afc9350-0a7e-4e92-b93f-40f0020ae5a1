# Certificates Directory

This directory should contain the client certificates required for SberRadar authentication.

## Required Files

- `sberradar.crt` - Client certificate for SberRadar API
- `sberradar.key` - Private key for the client certificate

## Security Note

**IMPORTANT**: Never commit actual certificate files to version control!

The `.gitignore` file is configured to exclude `*.crt`, `*.key`, and `*.pem` files from this directory.

## For Development

For development and testing purposes, you can create self-signed certificates:

```bash
# Generate private key
openssl genrsa -out sberradar.key 2048

# Generate certificate signing request
openssl req -new -key sberradar.key -out sberradar.csr

# Generate self-signed certificate (for testing only)
openssl x509 -req -days 365 -in sberradar.csr -signkey sberradar.key -out sberradar.crt

# Clean up CSR
rm sberradar.csr
```

## For Production

In production, you should:

1. Obtain proper certificates from your certificate authority
2. Store certificates securely (e.g., in Kubernetes secrets, HashiCorp Vault, etc.)
3. Use proper certificate rotation procedures
4. Monitor certificate expiration dates

## Docker Build

When building the Docker image, ensure the certificate files are present in this directory:

```bash
# Verify certificates are present
ls -la certs/
# Should show sberradar.crt and sberradar.key

# Build Docker image
make docker-build
```

## Kubernetes Deployment

For Kubernetes deployment, certificates are managed through Helm values:

```yaml
sberradar:
  certificate: "base64-encoded-certificate-content"
  privateKey: "base64-encoded-private-key-content"
```

Use the following commands to encode your certificates:

```bash
# Encode certificate
base64 -i certs/sberradar.crt

# Encode private key
base64 -i certs/sberradar.key
```
