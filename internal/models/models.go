package models

import "time"

// ZabbixMetric represents a metric from Zabbix
type ZabbixMetric struct {
	ItemID    string `json:"itemid"`
	Name      string `json:"name"`
	LastValue string `json:"lastvalue"`
	LastClock string `json:"lastclock"`
}

// SberRadarMetric represents a transformed metric for SberRadar
type SberRadarMetric struct {
	MetricName  string                 `json:"metric_name"`
	Value       interface{}            `json:"value"`
	Timestamp   int64                  `json:"timestamp"`
	Tags        map[string]string      `json:"tags,omitempty"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
}

// SberRadarPayload represents the payload sent to SberRadar
type SberRadarPayload struct {
	Source    string             `json:"source"`
	Timestamp int64              `json:"timestamp"`
	Metrics   []*SberRadarMetric `json:"metrics"`
}

// SyncResult represents the result of a metrics synchronization
type SyncResult struct {
	MetricsSent int
	ErrorCount  int
	Errors      []error
}

// TransformZabbixToSberRadar transforms Zabbix metrics to SberRadar format
func TransformZabbixToSberRadar(zabbixMetrics []*ZabbixMetric) ([]*SberRadarMetric, error) {
	sberRadarMetrics := make([]*SberRadarMetric, 0, len(zabbixMetrics))

	for _, zm := range zabbixMetrics {
		// Parse timestamp from Zabbix format
		timestamp := time.Now().Unix() // Default to current time
		// TODO: Parse zm.LastClock properly

		// Transform metric name (placeholder logic)
		metricName := transformMetricName(zm.Name)

		// Parse value (placeholder logic)
		value := parseMetricValue(zm.LastValue)

		sberRadarMetric := &SberRadarMetric{
			MetricName: metricName,
			Value:      value,
			Timestamp:  timestamp,
			Tags: map[string]string{
				"source":   "zabbix",
				"item_id":  zm.ItemID,
				"original": zm.Name,
			},
			Metadata: map[string]interface{}{
				"zabbix_item_id": zm.ItemID,
				"last_clock":     zm.LastClock,
			},
		}

		sberRadarMetrics = append(sberRadarMetrics, sberRadarMetric)
	}

	return sberRadarMetrics, nil
}

// transformMetricName transforms Zabbix metric name to SberRadar format
// This is a placeholder implementation - customize based on your requirements
func transformMetricName(zabbixName string) string {
	// Example transformation: replace spaces with underscores, convert to lowercase
	// TODO: Implement actual transformation logic based on requirements
	return "radar." + zabbixName
}

// parseMetricValue parses Zabbix metric value
// This is a placeholder implementation - customize based on your requirements
func parseMetricValue(value string) interface{} {
	// TODO: Implement proper value parsing (string, int, float, etc.)
	return value
}
