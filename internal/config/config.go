package config

import (
	"fmt"
	"os"
	"strconv"
)

// Config holds the application configuration
type Config struct {
	Server    ServerConfig    `yaml:"server"`
	Zabbix    ZabbixConfig    `yaml:"zabbix"`
	SberRadar SberRadarConfig `yaml:"sberradar"`
	Logging   LoggingConfig   `yaml:"logging"`
}

// ServerConfig holds HTTP server configuration
type ServerConfig struct {
	Port         int `yaml:"port"`
	ReadTimeout  int `yaml:"readTimeout"`
	WriteTimeout int `yaml:"writeTimeout"`
	IdleTimeout  int `yaml:"idleTimeout"`
}

// ZabbixConfig holds Zabbix API configuration
type ZabbixConfig struct {
	BaseURL      string `yaml:"baseUrl"`
	Username     string `yaml:"username"`
	Password     string `yaml:"password"`
	Timeout      int    `yaml:"timeout"`
	ConfigMapName string `yaml:"configMapName"`
	Namespace    string `yaml:"namespace"`
}

// SberRadarConfig holds SberRadar API configuration
type SberRadarConfig struct {
	BaseURL     string `yaml:"baseUrl"`
	CertPath    string `yaml:"certPath"`
	KeyPath     string `yaml:"keyPath"`
	Timeout     int    `yaml:"timeout"`
	MaxRetries  int    `yaml:"maxRetries"`
	RetryDelay  int    `yaml:"retryDelay"`
}

// LoggingConfig holds logging configuration
type LoggingConfig struct {
	Level  string `yaml:"level"`
	Format string `yaml:"format"`
}

// Load loads configuration from environment variables
func Load() (*Config, error) {
	cfg := &Config{
		Server: ServerConfig{
			Port:         getEnvAsInt("SERVER_PORT", 8080),
			ReadTimeout:  getEnvAsInt("SERVER_READ_TIMEOUT", 30),
			WriteTimeout: getEnvAsInt("SERVER_WRITE_TIMEOUT", 30),
			IdleTimeout:  getEnvAsInt("SERVER_IDLE_TIMEOUT", 60),
		},
		Zabbix: ZabbixConfig{
			BaseURL:       getEnv("ZABBIX_BASE_URL", ""),
			Username:      getEnv("ZABBIX_USERNAME", ""),
			Password:      getEnv("ZABBIX_PASSWORD", ""),
			Timeout:       getEnvAsInt("ZABBIX_TIMEOUT", 30),
			ConfigMapName: getEnv("ZABBIX_CONFIGMAP_NAME", "zabbix-metrics"),
			Namespace:     getEnv("KUBERNETES_NAMESPACE", "default"),
		},
		SberRadar: SberRadarConfig{
			BaseURL:    getEnv("SBERRADAR_BASE_URL", ""),
			CertPath:   getEnv("SBERRADAR_CERT_PATH", "/etc/ssl/certs/sberradar.crt"),
			KeyPath:    getEnv("SBERRADAR_KEY_PATH", "/etc/ssl/certs/sberradar.key"),
			Timeout:    getEnvAsInt("SBERRADAR_TIMEOUT", 30),
			MaxRetries: getEnvAsInt("SBERRADAR_MAX_RETRIES", 3),
			RetryDelay: getEnvAsInt("SBERRADAR_RETRY_DELAY", 5),
		},
		Logging: LoggingConfig{
			Level:  getEnv("LOG_LEVEL", "info"),
			Format: getEnv("LOG_FORMAT", "json"),
		},
	}

	// Validate required configuration
	if err := cfg.Validate(); err != nil {
		return nil, fmt.Errorf("configuration validation failed: %w", err)
	}

	return cfg, nil
}

// Validate validates the configuration
func (c *Config) Validate() error {
	if c.Zabbix.BaseURL == "" {
		return fmt.Errorf("ZABBIX_BASE_URL is required")
	}
	if c.Zabbix.Username == "" {
		return fmt.Errorf("ZABBIX_USERNAME is required")
	}
	if c.Zabbix.Password == "" {
		return fmt.Errorf("ZABBIX_PASSWORD is required")
	}
	if c.SberRadar.BaseURL == "" {
		return fmt.Errorf("SBERRADAR_BASE_URL is required")
	}
	return nil
}

// getEnv gets an environment variable with a default value
func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

// getEnvAsInt gets an environment variable as integer with a default value
func getEnvAsInt(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
	}
	return defaultValue
}
