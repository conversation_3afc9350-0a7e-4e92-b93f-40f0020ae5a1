package services

import (
	"context"
	"fmt"

	"radar-integrator/internal/models"

	"github.com/sirupsen/logrus"
)

// IntegrationService orchestrates the integration between Zabbix and SberRadar
type IntegrationService struct {
	zabbixService    *ZabbixService
	sberRadarService *SberRadarService
	logger           *logrus.Logger
}

// NewIntegrationService creates a new integration service
func NewIntegrationService(zabbixService *ZabbixService, sberRadarService *SberRadarService, logger *logrus.Logger) *IntegrationService {
	return &IntegrationService{
		zabbixService:    zabbixService,
		sberRadarService: sberRadarService,
		logger:           logger,
	}
}

// SyncMetrics performs the complete metrics synchronization workflow
func (i *IntegrationService) SyncMetrics(ctx context.Context) (*models.SyncResult, error) {
	result := &models.SyncResult{
		Errors: make([]error, 0),
	}

	i.logger.Info("Starting metrics synchronization workflow")

	// Step 1: Get metrics list from ConfigMap
	metricNames, err := i.zabbixService.GetMetricsFromConfigMap(ctx)
	if err != nil {
		return result, fmt.Errorf("failed to get metrics from ConfigMap: %w", err)
	}

	if len(metricNames) == 0 {
		i.logger.Warn("No metrics found in ConfigMap")
		return result, nil
	}

	// Step 2: Fetch metrics from Zabbix
	zabbixMetrics, err := i.zabbixService.FetchMetrics(ctx, metricNames)
	if err != nil {
		return result, fmt.Errorf("failed to fetch metrics from Zabbix: %w", err)
	}

	if len(zabbixMetrics) == 0 {
		i.logger.Warn("No metrics fetched from Zabbix")
		return result, nil
	}

	// Step 3: Transform metrics to SberRadar format
	sberRadarMetrics, err := models.TransformZabbixToSberRadar(zabbixMetrics)
	if err != nil {
		return result, fmt.Errorf("failed to transform metrics: %w", err)
	}

	// Step 4: Send metrics to SberRadar
	err = i.sberRadarService.SendMetrics(ctx, sberRadarMetrics)
	if err != nil {
		result.ErrorCount++
		result.Errors = append(result.Errors, err)
		return result, fmt.Errorf("failed to send metrics to SberRadar: %w", err)
	}

	result.MetricsSent = len(sberRadarMetrics)

	i.logger.WithFields(logrus.Fields{
		"metrics_fetched":    len(zabbixMetrics),
		"metrics_sent":       result.MetricsSent,
		"error_count":        result.ErrorCount,
	}).Info("Metrics synchronization completed successfully")

	return result, nil
}

// HealthCheck performs health checks on all integrated services
func (i *IntegrationService) HealthCheck(ctx context.Context) error {
	// Check SberRadar connectivity
	if err := i.sberRadarService.HealthCheck(ctx); err != nil {
		return fmt.Errorf("SberRadar health check failed: %w", err)
	}

	// Check Zabbix connectivity by attempting authentication
	if err := i.zabbixService.Authenticate(ctx); err != nil {
		return fmt.Errorf("Zabbix health check failed: %w", err)
	}

	return nil
}
