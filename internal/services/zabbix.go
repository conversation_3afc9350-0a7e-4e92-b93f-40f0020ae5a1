package services

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"time"

	"radar-integrator/internal/config"
	"radar-integrator/internal/models"

	"github.com/sirupsen/logrus"
)

// ZabbixService handles Zabbix API interactions
type ZabbixService struct {
	config     config.ZabbixConfig
	logger     *logrus.Logger
	httpClient *http.Client
	authToken  string
}

// NewZabbixService creates a new Zabbix service
func NewZabbixService(cfg config.ZabbixConfig, logger *logrus.Logger) *ZabbixService {
	httpClient := &http.Client{
		Timeout: time.Duration(cfg.Timeout) * time.Second,
	}

	return &ZabbixService{
		config:     cfg,
		logger:     logger,
		httpClient: httpClient,
	}
}

// ZabbixRequest represents a Zabbix API request
type ZabbixRequest struct {
	JSONRPC string      `json:"jsonrpc"`
	Method  string      `json:"method"`
	Params  interface{} `json:"params"`
	ID      int         `json:"id"`
	Auth    string      `json:"auth,omitempty"`
}

// ZabbixResponse represents a Zabbix API response
type ZabbixResponse struct {
	JSONRPC string          `json:"jsonrpc"`
	Result  json.RawMessage `json:"result"`
	Error   *ZabbixError    `json:"error"`
	ID      int             `json:"id"`
}

// ZabbixError represents a Zabbix API error
type ZabbixError struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Data    string `json:"data"`
}

// LoginParams represents Zabbix login parameters
type LoginParams struct {
	User     string `json:"user"`
	Password string `json:"password"`
}

// Authenticate authenticates with Zabbix API and stores the auth token
func (z *ZabbixService) Authenticate(ctx context.Context) error {
	loginParams := LoginParams{
		User:     z.config.Username,
		Password: z.config.Password,
	}

	request := ZabbixRequest{
		JSONRPC: "2.0",
		Method:  "user.login",
		Params:  loginParams,
		ID:      1,
	}

	response, err := z.makeRequest(ctx, request)
	if err != nil {
		return fmt.Errorf("authentication failed: %w", err)
	}

	if response.Error != nil {
		return fmt.Errorf("authentication error: %s", response.Error.Message)
	}

	var token string
	if err := json.Unmarshal(response.Result, &token); err != nil {
		return fmt.Errorf("failed to parse auth token: %w", err)
	}

	z.authToken = token
	z.logger.Info("Successfully authenticated with Zabbix")
	return nil
}

// GetMetricsFromFile reads the list of metrics from mounted configuration file
func (z *ZabbixService) GetMetricsFromFile(ctx context.Context) ([]string, error) {
	metricsData, err := os.ReadFile(z.config.MetricsFile)
	if err != nil {
		return nil, fmt.Errorf("failed to read metrics file %s: %w", z.config.MetricsFile, err)
	}

	var metrics []string
	if err := json.Unmarshal(metricsData, &metrics); err != nil {
		return nil, fmt.Errorf("failed to parse metrics from file %s: %w", z.config.MetricsFile, err)
	}

	z.logger.WithFields(logrus.Fields{
		"file":  z.config.MetricsFile,
		"count": len(metrics),
	}).Info("Loaded metrics from file")

	return metrics, nil
}

// FetchMetrics fetches metrics from Zabbix API
func (z *ZabbixService) FetchMetrics(ctx context.Context, metricNames []string) ([]*models.ZabbixMetric, error) {
	if z.authToken == "" {
		if err := z.Authenticate(ctx); err != nil {
			return nil, err
		}
	}

	// For now, create a simple item.get request
	// In a real implementation, you'd need to map metric names to Zabbix item IDs
	params := map[string]interface{}{
		"output": []string{"itemid", "name", "lastvalue", "lastclock"},
		"filter": map[string]interface{}{
			"name": metricNames,
		},
	}

	request := ZabbixRequest{
		JSONRPC: "2.0",
		Method:  "item.get",
		Params:  params,
		ID:      2,
		Auth:    z.authToken,
	}

	response, err := z.makeRequest(ctx, request)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch metrics: %w", err)
	}

	if response.Error != nil {
		return nil, fmt.Errorf("Zabbix API error: %s", response.Error.Message)
	}

	var items []map[string]interface{}
	if err := json.Unmarshal(response.Result, &items); err != nil {
		return nil, fmt.Errorf("failed to parse metrics response: %w", err)
	}

	metrics := make([]*models.ZabbixMetric, 0, len(items))
	for _, item := range items {
		metric := &models.ZabbixMetric{
			ItemID:    fmt.Sprintf("%v", item["itemid"]),
			Name:      fmt.Sprintf("%v", item["name"]),
			LastValue: fmt.Sprintf("%v", item["lastvalue"]),
			LastClock: fmt.Sprintf("%v", item["lastclock"]),
		}
		metrics = append(metrics, metric)
	}

	z.logger.WithField("count", len(metrics)).Info("Fetched metrics from Zabbix")
	return metrics, nil
}

// makeRequest makes an HTTP request to Zabbix API
func (z *ZabbixService) makeRequest(ctx context.Context, request ZabbixRequest) (*ZabbixResponse, error) {
	jsonData, err := json.Marshal(request)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %w", err)
	}

	req, err := http.NewRequestWithContext(ctx, "POST", z.config.BaseURL+"/api_jsonrpc.php", bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Content-Type", "application/json")

	resp, err := z.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("HTTP request failed: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("HTTP error %d: %s", resp.StatusCode, string(body))
	}

	var response ZabbixResponse
	if err := json.Unmarshal(body, &response); err != nil {
		return nil, fmt.Errorf("failed to unmarshal response: %w", err)
	}

	return &response, nil
}
