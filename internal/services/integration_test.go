package services

import (
	"context"
	"testing"

	"radar-integrator/internal/config"
	"radar-integrator/internal/models"

	"github.com/sirupsen/logrus"
)

func TestTransformZabbixToSberRadar(t *testing.T) {
	// Test data
	zabbixMetrics := []*models.ZabbixMetric{
		{
			ItemID:    "12345",
			Name:      "system.cpu.util",
			LastValue: "75.5",
			LastClock: "1609459200",
		},
		{
			ItemID:    "12346",
			Name:      "vm.memory.util",
			LastValue: "60.2",
			LastClock: "1609459200",
		},
	}

	// Transform metrics
	sberRadarMetrics, err := models.TransformZabbixToSberRadar(zabbixMetrics)
	if err != nil {
		t.Fatalf("Failed to transform metrics: %v", err)
	}

	// Verify transformation
	if len(sberRadarMetrics) != len(zabbixMetrics) {
		t.Errorf("Expected %d metrics, got %d", len(zabbixMetrics), len(sberRadarMetrics))
	}

	for i, metric := range sberRadarMetrics {
		if metric.MetricName == "" {
			t.Errorf("Metric %d has empty name", i)
		}
		if metric.Value == nil {
			t.Errorf("Metric %d has nil value", i)
		}
		if metric.Timestamp == 0 {
			t.Errorf("Metric %d has zero timestamp", i)
		}
		if metric.Tags["source"] != "zabbix" {
			t.Errorf("Metric %d missing source tag", i)
		}
	}
}

func TestIntegrationServiceHealthCheck(t *testing.T) {
	logger := logrus.New()
	logger.SetLevel(logrus.ErrorLevel) // Reduce noise in tests

	// Create mock configurations
	zabbixConfig := config.ZabbixConfig{
		BaseURL:  "http://mock-zabbix",
		Username: "test",
		Password: "test",
		Timeout:  30,
	}

	sberRadarConfig := config.SberRadarConfig{
		BaseURL:    "https://mock-sberradar",
		CertPath:   "/tmp/test.crt",
		KeyPath:    "/tmp/test.key",
		Timeout:    30,
		MaxRetries: 3,
		RetryDelay: 1,
	}

	// Create services
	zabbixService := NewZabbixService(zabbixConfig, logger)
	sberRadarService := NewSberRadarService(sberRadarConfig, logger)
	integrationService := NewIntegrationService(zabbixService, sberRadarService, logger)

	// Test health check (will fail due to mock endpoints, but should not panic)
	ctx := context.Background()
	err := integrationService.HealthCheck(ctx)

	// We expect this to fail since we're using mock endpoints
	if err == nil {
		t.Log("Health check passed (unexpected with mock endpoints)")
	} else {
		t.Logf("Health check failed as expected: %v", err)
	}
}
