# Multi-stage build for Go application
FROM golang:1.21-alpine AS builder

# Install git and ca-certificates (needed for go mod download)
RUN apk add --no-cache git ca-certificates tzdata

# Set working directory
WORKDIR /app

# Copy go mod files
COPY go.mod go.sum ./

# Download dependencies
RUN go mod download

# Copy source code
COPY . .

# Build the application
RUN CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build \
    -ldflags='-w -s -extldflags "-static"' \
    -a -installsuffix cgo \
    -o radar-integrator \
    ./main.go

# Final stage - minimal Alpine image
FROM alpine:3.18

## Install ca-certificates for HTTPS requests
#RUN apk --no-cache add ca-certificates tzdata

# Create non-root user
RUN addgroup -g 1001 -S appgroup && \
    adduser -u 1001 -S appuser -G appgroup

# Set working directory
WORKDIR /app

# Copy the binary from builder stage
COPY --from=builder /app/radar-integrator .

# Create directories for certificates
RUN mkdir -p /etc/ssl/certs && \
    chown -R appuser:appgroup /etc/ssl/certs

# Copy certificate files (these should be provided during build)
# The certificate files should be placed in the build context
COPY certs/sberradar.crt /etc/ssl/certs/sberradar.crt
COPY certs/sberradar.key /etc/ssl/certs/sberradar.key

# Set proper permissions for certificate files
RUN chmod 644 /etc/ssl/certs/sberradar.crt && \
    chmod 600 /etc/ssl/certs/sberradar.key && \
    chown appuser:appgroup /etc/ssl/certs/sberradar.*

# Change ownership of the app directory
RUN chown -R appuser:appgroup /app

# Switch to non-root user
USER appuser

# Expose port
EXPOSE 8080

# Health check
#HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
#    CMD wget --no-verbose --tries=1 --spider http://localhost:8080/health || exit 1

# Run the application
ENTRYPOINT ["./radar-integrator"]
