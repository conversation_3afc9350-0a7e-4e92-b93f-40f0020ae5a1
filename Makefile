# Makefile for radar-integrator

# Variables
APP_NAME := radar-integrator
VERSION := $(shell git describe --tags --always --dirty 2>/dev/null || echo "dev")
DOCKER_REGISTRY := your-registry.com
DOCKER_IMAGE := $(DOCKER_REGISTRY)/$(APP_NAME)
HELM_CHART_PATH := helm/$(APP_NAME)
NAMESPACE_DEV := radar-integrator-dev
NAMESPACE_PROD := radar-integrator-prod

# Go variables
GOCMD := go
GOBUILD := $(GOCMD) build
GOCLEAN := $(GOCMD) clean
GOTEST := $(GOCMD) test
GOGET := $(GOCMD) get
GOMOD := $(GOCMD) mod
GOFMT := gofmt
GOLINT := golangci-lint

# Build variables
BINARY_NAME := $(APP_NAME)
BINARY_PATH := ./$(BINARY_NAME)
LDFLAGS := -ldflags "-X main.version=$(VERSION) -w -s"

.PHONY: help
help: ## Display this help message
	@echo "Available targets:"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  %-20s %s\n", $$1, $$2}' $(MAKEFILE_LIST)

# Development targets
.PHONY: dev-setup
dev-setup: ## Set up development environment
	@echo "Setting up development environment..."
	$(GOMOD) download
	$(GOMOD) tidy
	@echo "Installing development tools..."
	$(GOGET) github.com/golangci/golangci-lint/cmd/golangci-lint@latest

.PHONY: run
run: ## Run the application locally
	@echo "Running $(APP_NAME)..."
	$(GOCMD) run main.go

.PHONY: build
build: ## Build the application binary
	@echo "Building $(APP_NAME)..."
	CGO_ENABLED=0 GOOS=linux GOARCH=amd64 $(GOBUILD) $(LDFLAGS) -o $(BINARY_PATH) ./main.go

.PHONY: clean
clean: ## Clean build artifacts
	@echo "Cleaning..."
	$(GOCLEAN)
	rm -f $(BINARY_PATH)
	docker rmi $(DOCKER_IMAGE):$(VERSION) 2>/dev/null || true

# Testing targets
.PHONY: test
test: ## Run tests
	@echo "Running tests..."
	$(GOTEST) -v -race -coverprofile=coverage.out ./...

.PHONY: test-coverage
test-coverage: test ## Run tests with coverage report
	@echo "Generating coverage report..."
	$(GOCMD) tool cover -html=coverage.out -o coverage.html
	@echo "Coverage report generated: coverage.html"

.PHONY: benchmark
benchmark: ## Run benchmarks
	@echo "Running benchmarks..."
	$(GOTEST) -bench=. -benchmem ./...

# Code quality targets
.PHONY: fmt
fmt: ## Format Go code
	@echo "Formatting code..."
	$(GOFMT) -s -w .

.PHONY: lint
lint: ## Run linter
	@echo "Running linter..."
	$(GOLINT) run ./...

.PHONY: vet
vet: ## Run go vet
	@echo "Running go vet..."
	$(GOCMD) vet ./...

.PHONY: check
check: fmt vet lint test ## Run all code quality checks

# Docker targets
.PHONY: docker-build
docker-build: ## Build Docker image
	@echo "Building Docker image $(DOCKER_IMAGE):$(VERSION)..."
	docker build -t $(DOCKER_IMAGE):$(VERSION) .
	docker tag $(DOCKER_IMAGE):$(VERSION) $(DOCKER_IMAGE):latest

.PHONY: docker-push
docker-push: docker-build ## Push Docker image to registry
	@echo "Pushing Docker image..."
	docker push $(DOCKER_IMAGE):$(VERSION)
	docker push $(DOCKER_IMAGE):latest

.PHONY: docker-run
docker-run: docker-build ## Run Docker container locally
	@echo "Running Docker container..."
	docker run --rm -p 8080:8080 \
		-e ZABBIX_BASE_URL=http://localhost:8080 \
		-e ZABBIX_USERNAME=admin \
		-e ZABBIX_PASSWORD=zabbix \
		-e SBERRADAR_BASE_URL=https://localhost:8443 \
		$(DOCKER_IMAGE):$(VERSION)

# Helm targets
.PHONY: helm-lint
helm-lint: ## Lint Helm chart
	@echo "Linting Helm chart..."
	helm lint $(HELM_CHART_PATH)

.PHONY: helm-template
helm-template: ## Generate Helm templates
	@echo "Generating Helm templates..."
	helm template $(APP_NAME) $(HELM_CHART_PATH) --values $(HELM_CHART_PATH)/values.yaml

.PHONY: helm-package
helm-package: helm-lint ## Package Helm chart
	@echo "Packaging Helm chart..."
	helm package $(HELM_CHART_PATH) --destination ./charts/

# Deployment targets
.PHONY: deploy-dev
deploy-dev: docker-push helm-lint ## Deploy to development environment
	@echo "Deploying to development environment..."
	helm upgrade --install $(APP_NAME)-dev $(HELM_CHART_PATH) \
		--namespace $(NAMESPACE_DEV) \
		--create-namespace \
		--values $(HELM_CHART_PATH)/values.yaml \
		--values $(HELM_CHART_PATH)/values.dev.yaml \
		--set image.tag=$(VERSION) \
		--wait

.PHONY: deploy-prod
deploy-prod: docker-push helm-lint ## Deploy to production environment
	@echo "Deploying to production environment..."
	helm upgrade --install $(APP_NAME)-prod $(HELM_CHART_PATH) \
		--namespace $(NAMESPACE_PROD) \
		--create-namespace \
		--values $(HELM_CHART_PATH)/values.yaml \
		--values $(HELM_CHART_PATH)/values.prod.yaml \
		--set image.tag=$(VERSION) \
		--wait

.PHONY: undeploy-dev
undeploy-dev: ## Remove development deployment
	@echo "Removing development deployment..."
	helm uninstall $(APP_NAME)-dev --namespace $(NAMESPACE_DEV)

.PHONY: undeploy-prod
undeploy-prod: ## Remove production deployment
	@echo "Removing production deployment..."
	helm uninstall $(APP_NAME)-prod --namespace $(NAMESPACE_PROD)

# Utility targets
.PHONY: logs-dev
logs-dev: ## Show development logs
	kubectl logs -f deployment/$(APP_NAME)-dev -n $(NAMESPACE_DEV)

.PHONY: logs-prod
logs-prod: ## Show production logs
	kubectl logs -f deployment/$(APP_NAME)-prod -n $(NAMESPACE_PROD)

.PHONY: status-dev
status-dev: ## Show development deployment status
	kubectl get all -n $(NAMESPACE_DEV)

.PHONY: status-prod
status-prod: ## Show production deployment status
	kubectl get all -n $(NAMESPACE_PROD)

.PHONY: port-forward-dev
port-forward-dev: ## Port forward development service
	kubectl port-forward service/$(APP_NAME)-dev 8080:80 -n $(NAMESPACE_DEV)

.PHONY: port-forward-prod
port-forward-prod: ## Port forward production service
	kubectl port-forward service/$(APP_NAME)-prod 8080:80 -n $(NAMESPACE_PROD)

# CI/CD targets
.PHONY: ci
ci: check test docker-build ## Run CI pipeline
	@echo "CI pipeline completed successfully"

.PHONY: cd-dev
cd-dev: ci deploy-dev ## Run CD pipeline for development
	@echo "CD pipeline for development completed successfully"

.PHONY: cd-prod
cd-prod: ci deploy-prod ## Run CD pipeline for production
	@echo "CD pipeline for production completed successfully"

# Security targets
.PHONY: security-scan
security-scan: ## Run security scan on Docker image
	@echo "Running security scan..."
	docker run --rm -v /var/run/docker.sock:/var/run/docker.sock \
		aquasec/trivy image $(DOCKER_IMAGE):$(VERSION)

.PHONY: deps-check
deps-check: ## Check for dependency vulnerabilities
	@echo "Checking dependencies for vulnerabilities..."
	$(GOCMD) list -json -m all | nancy sleuth
