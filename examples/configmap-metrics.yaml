apiVersion: v1
kind: ConfigMap
metadata:
  name: radar-integrator-metrics
  namespace: radar-integrator-dev
  labels:
    app: radar-integrator
    component: config
data:
  metrics.json: |
    [
      "system.cpu.util[,avg1]",
      "system.cpu.util[,avg5]", 
      "system.cpu.util[,avg15]",
      "vm.memory.util",
      "vm.memory.size[available]",
      "vm.memory.size[total]",
      "net.if.in[eth0]",
      "net.if.out[eth0]",
      "system.load[percpu,avg1]",
      "system.load[percpu,avg5]",
      "system.load[percpu,avg15]",
      "vfs.fs.size[/,used]",
      "vfs.fs.size[/,total]",
      "vfs.fs.size[/,pfree]",
      "system.uptime",
      "proc.num[,,run]",
      "proc.num[,,sleep]",
      "system.swap.size[,total]",
      "system.swap.size[,free]"
    ]
