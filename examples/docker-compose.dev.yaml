version: '3.8'

services:
  radar-integrator:
    build:
      context: ..
      dockerfile: Dockerfile
    ports:
      - "8080:8080"
    environment:
      - LOG_LEVEL=debug
      - ZABBIX_BASE_URL=http://zabbix:8080
      - ZABBIX_USERNAME=admin
      - ZABBIX_PASSWORD=zabbix
      - SBERRADAR_BASE_URL=https://sberradar-mock:8443
      - SBERRADAR_TIMEOUT=60
      - SBERRADAR_MAX_RETRIES=5
    volumes:
      - ./certs:/etc/ssl/certs:ro
    depends_on:
      - zabbix-mock
      - sberradar-mock
    networks:
      - radar-network

  # Mock Zabbix server for development
  zabbix-mock:
    image: nginx:alpine
    ports:
      - "8081:80"
    volumes:
      - ./mock-responses/zabbix:/usr/share/nginx/html:ro
    networks:
      - radar-network

  # Mock SberRadar server for development  
  sberradar-mock:
    image: nginx:alpine
    ports:
      - "8443:443"
    volumes:
      - ./mock-responses/sberradar:/usr/share/nginx/html:ro
      - ./certs:/etc/nginx/certs:ro
    networks:
      - radar-network

networks:
  radar-network:
    driver: bridge
