# Example environment configuration for radar-integrator
# Copy this file to .env and update with your actual values

# Server configuration
SERVER_PORT=8080
LOG_LEVEL=info
LOG_FORMAT=json

# Zabbix configuration
ZABBIX_BASE_URL=http://your-zabbix-server.com
ZABBIX_USERNAME=your-zabbix-username
ZABBIX_PASSWORD=your-zabbix-password
ZABBIX_TIMEOUT=30
ZABBIX_CONFIGMAP_NAME=zabbix-metrics
KUBERNETES_NAMESPACE=default

# SberRadar configuration
SBERRADAR_BASE_URL=https://your-sberradar-server.com
SBERRADAR_CERT_PATH=/etc/ssl/certs/sberradar.crt
SBERRADAR_KEY_PATH=/etc/ssl/certs/sberradar.key
SBERRADAR_TIMEOUT=30
SBERRADAR_MAX_RETRIES=3
SBERRADAR_RETRY_DELAY=5
