# Radar Integrator

A Go application that integrates Zabbix monitoring metrics with SberRadar by fetching metrics from Zabbix API and transforming them for SberRadar consumption.

## Features

- **Zabbix Integration**: Fetches metrics from Zabbix API with authentication
- **SberRadar Integration**: Sends transformed metrics to SberRadar with client certificate authentication (SSL verification disabled)
- **File-based Configuration**: Reads metric configuration from mounted files (no Kubernetes API access required)
- **Health Checks**: Built-in health and readiness endpoints
- **Secure**: Uses client certificates for SberRadar authentication
- **Production Ready**: Includes Helm charts for easy deployment

## Architecture

```
┌─────────────┐    ┌──────────────────┐    ┌─────────────┐
│   Zabbix    │───▶│ Radar Integrator │───▶│ SberRadar   │
│     API     │    │                  │    │     API     │
└─────────────┘    └──────────────────┘    └─────────────┘
                            │
                            ▼
                   ┌─────────────────┐
                   │ Kubernetes      │
                   │ ConfigMap       │
                   │ (Metrics List)  │
                   └─────────────────┘
```

## Quick Start

### Prerequisites

- Go 1.21+
- Docker
- Kubernetes cluster
- Helm 3.x
- Make

### Local Development

1. **Set up development environment:**
   ```bash
   make dev-setup
   ```

2. **Run locally:**
   ```bash
   export ZABBIX_BASE_URL="http://your-zabbix-server"
   export ZABBIX_USERNAME="your-username"
   export ZABBIX_PASSWORD="your-password"
   export SBERRADAR_BASE_URL="https://your-sberradar-server"
   make run
   ```

3. **Run tests:**
   ```bash
   make test
   make test-coverage
   ```

### Docker Build

```bash
# Build Docker image
make docker-build

# Run Docker container
make docker-run
```

### Kubernetes Deployment

1. **Deploy to development:**
   ```bash
   make deploy-dev
   ```

2. **Deploy to production:**
   ```bash
   make deploy-prod
   ```

## Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `SERVER_PORT` | HTTP server port | `8080` |
| `LOG_LEVEL` | Logging level (debug, info, warn, error) | `info` |
| `ZABBIX_BASE_URL` | Zabbix server URL | Required |
| `ZABBIX_USERNAME` | Zabbix username | Required |
| `ZABBIX_PASSWORD` | Zabbix password | Required |
| `ZABBIX_TIMEOUT` | Zabbix API timeout (seconds) | `30` |
| `ZABBIX_METRICS_FILE` | Path to metrics configuration file | `/etc/config/metrics.json` |
| `SBERRADAR_BASE_URL` | SberRadar API URL | Required |
| `SBERRADAR_TIMEOUT` | SberRadar API timeout (seconds) | `30` |
| `SBERRADAR_MAX_RETRIES` | Maximum retry attempts | `3` |
| `SBERRADAR_RETRY_DELAY` | Delay between retries (seconds) | `5` |

### Metrics Configuration File

The application reads the list of metrics to fetch from a mounted JSON file. The file is mounted from a Kubernetes ConfigMap. Example:

```json
[
  "system.cpu.util",
  "vm.memory.util",
  "net.if.in",
  "net.if.out"
]
```

This file is automatically mounted to `/etc/config/metrics.json` in the container via Kubernetes ConfigMap volume mount.

## API Endpoints

- `GET /health` - Health check endpoint
- `GET /ready` - Readiness check endpoint
- `POST /metrics/sync` - Trigger metrics synchronization

## Certificate Setup

For SberRadar integration, you need to provide client certificates:

1. **For Docker build:**
   ```bash
   mkdir -p certs/
   cp your-cert.crt certs/sberradar.crt
   cp your-key.key certs/sberradar.key
   ```

2. **For Helm deployment:**
   Update the certificate values in `values.dev.yaml` or `values.prod.yaml`:
   ```yaml
   sberradar:
     certificate: "base64-encoded-certificate"
     privateKey: "base64-encoded-private-key"
   ```

## Development

### Code Structure

```
├── main.go                 # Application entry point
├── internal/
│   ├── config/            # Configuration management
│   ├── handlers/          # HTTP handlers
│   ├── models/            # Data models
│   └── services/          # Business logic
├── helm/                  # Helm chart
├── certs/                 # Certificate files (for Docker build)
├── Dockerfile             # Docker image definition
└── Makefile              # Build and deployment automation
```

### Available Make Targets

Run `make help` to see all available targets.

## Monitoring

The application exposes metrics and logs in JSON format for easy integration with monitoring systems.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run tests: `make check`
5. Submit a pull request

## License

[Add your license information here]
