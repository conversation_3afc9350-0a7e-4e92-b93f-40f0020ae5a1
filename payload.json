{
  "$schema": "http://json-schema.org/draft-04/schema#",
  "description": "Схема описывает сообшение с метаданными метрики. Связь с данными и
  порогами метрик осуществляется по metric_hash. Содержание метаданных фиксируется перед
  отправкой данных на оперативный дашборд и не изменяется. ",
  "type": "object",
  "properties": {
    "metric_value": {
      "description": "Число с плавающей точкой, например 7.0",
      "type": "number",
      "minimum": -4503599627370496,
      "maximum": 4503599627370496
    },
    "metric_timestamp": {
      "type": "integer",
      "description": "Unix timestamp в секундах c 01/01/1970 в UTC (без
      timezone), обозначающий момент времени, в который метрика и baseline имели значения,
      указанные в полях metric_value и baseline_value соответственно"
    ,
      "minimum": 0,
      "maximum": 3147483648
    },
    "baseline_value": {
      "description": "Значение базовой линии метрики, рассчитанное средствами
      системы мониторинга. Может не передаваться. Используется для интерпретации значения метрики
      в сочетании с порогами. ",
      "anyOf": [
        {
          "type": "number",
          "minimum": -4503599627370496,
          "description": "Число с плавающей точкой, например 2.28",
          "maximum": 4503599627370496
        },
        {
          "type": "null",
          "description": "... или null, если передача не производится"
        }
      ]
    },
    "metric_actual_date": {
      "type": "integer",
      "description": "Unix timestamp в секундах c 01/01/1970 в UTC (без
      timezone), обозначающий момент времени, в который проводилась актуализации правила сбора
      метрики в системе мониторинга"
    ,
      "minimum": 0,
      "maximum": 3147483648
    },
    "version": {
      "type": "string",
      "maxLength": 16,
      "description": "Версия формата сообщения, определяется командой SberRadar.
      Для текущей версии спецификации равен \"1.0.0\""
    },
    "it_service_ci": {
      "type": "string",
      "maxLength": 10,
      "pattern": "^CI[0-9]{8}$",
      "description": "ID КЭ ИТ-услуги, по которой собирается метрика",
    },
    "object_ci": {
      "type": [
        "string",
        "null"
      ],
      "maxLength": 10,
      "pattern": "^CI[0-9]{8}$",
      "description": "ID КЭ объекта мониторинга. В случае невозможности определить
      для объекта - передать null"
    },
    "metric_name": {
      "type": "string",
      "maxLength": 255,
      "pattern": "^[A-Za-z0-9А-Яа-яЁё_\\-\\.\\ ]{3,255}$",
      "description": "Название правила сбора метрики в системе мониторинга."
    },
    "metric_description": {
      "type": "string",
      "maxLength": 255,
      "pattern": "^[A-Za-z0-9А-Яа-яЁё_\\-\\.\\ ]{3,255}$",
      "description": "Описание метрики, характеризующее основные свойства метрики
      (что измеряется, о чем сигнализирует и т.п). Чем понятнее описание, тем легче определить
      причину отклонения и собрать правильные компетенции для его устранения."
    },
    "metric_type": {
      "type": "string",
      "description": "Тип метрики в соответствии со Стандартом технологического
      выбирается из списка допустимых значений"
    ,
      "maxLength": 50,
      "enum": [
        "AVAILABILITY",
        "LATENCY",
        "REQ_IN_SUCCESS",
        "REQ_OUT_SUCCESS",
        "REQ_IN_ERROR",
        "REQ_OUT_ERROR",
        "SYNTHETIC",
        "SATURATION",
        "OTHER"
      ]
    },
    "metric_unit": {
      "type": "string",
      "maxLength": 32,
      "description": "Единица измерения метрики. Если применимо - рекомендуется
      использовать национальное кодовое обозначение единицы измерения метрики согласно
      Общероссийскому Классификатору Единиц Измерения (ОКЕИ, https://classifikators.ru/
      okei)."
    },
    "metric_period_sec": {
      "type": "integer",
      "description": "Если применимо - интервал сбора данных метрики в целых
      секундах.",
      "minimum": 1,
      "maximum": 86400
    },
    "threshold_normal_min": {
      "type": [
        "number",
        "null"
      ],
      "description": "Нижняя граница порога. Может быть числом или null. В случае
      null интерпретируется как минус бесконечность"
    ,
      "minimum": -1e+99,
      "maximum": 1e+99
    },
    "combination_selector": {
      "type": "string",
      "description": "Переключатель, определяющий способ выбора статуса метрики
      при попадании значения метрики в 2 и более диапазона. Если best - то берем наименьшую
      критичность, если worst (или параметр отсутствует) - наибольшую среди пересекающихся
      коридоров в которые попало значение метрики. Это применимо и к описанию пороговых значений
      (threshold), и к описанию отклонений от базовой линии (baseline_deviation). В случае,
      если пороги не передаются, в combination_selector указывается null",
      "enum": [
        "best",
        "worst"
      ],
      "maxLength": 20
    },
    "threshold_normal_max": {
      "type": [
        "number",
        "null"
      ],
      "description": "Нижняя граница порога. Может быть числом или null. В случае
      null интерпретируется как минус бесконечность"
    ,
      "minimum": -1e+99,
      "maximum": 1e+99
    },
    "threshold_warning_min": {
      "type": [
        "number",
        "null"
      ],
      "description": "Нижняя граница порога. Может быть числом или null. В случае
      null интерпретируется как минус бесконечность"
    ,
      "minimum": -1e+99,
      "maximum": 1e+99
    },
    "threshold_warning_max": {
      "type": [
        "number",
        "null"
      ],
      "description": "Нижняя граница порога. Может быть числом или null. В случае
      null интерпретируется как минус бесконечность"
    ,
      "minimum": -1e+99,
      "maximum": 1e+99
    },
    "threshold_high_min": {
      "type": [
        "number",
        "null"
      ],
      "description": "Нижняя граница порога. Может быть числом или null. В случае
      null интерпретируется как минус бесконечность"
    ,
      "minimum": -1e+99,
      "maximum": 1e+99
    },
    "threshold_high_max": {
      "type": [
        "number",
        "null"
      ],
      "description": "Нижняя граница порога. Может быть числом или null. В случае
      null интерпретируется как минус бесконечность"
    ,
      "minimum": -1e+99,
      "maximum": 1e+99
    },
    "threshold_critical_min": {
      "type": [
        "number",
        "null"
      ],
      "description": "Нижняя граница порога. Может быть числом или null. В случае
      null интерпретируется как минус бесконечность"
    ,
      "minimum": -1e+99,
      "maximum": 1e+99
    },
    "threshold_critical_max": {
      "type": [
        "number",
        "null"
      ],
      "description": "Нижняя граница порога. Может быть числом или null. В случае
      null интерпретируется как минус бесконечность"
    ,
      "minimum": -1e+99,
      "maximum": 1e+99
    },
    "threshold_wide_ranging_min": {
      "type": [
        "number",
        "null"
      ],
      "description": "Нижняя граница порога. Может быть числом или null. В случае
      null интерпретируется как минус бесконечность"
    ,
      "minimum": -1e+99,
      "maximum": 1e+99
    },
    "threshold_wide_ranging_max": {
      "type": [
        "number",
        "null"
      ],
      "description": "Нижняя граница порога. Может быть числом или null. В случае
      null интерпретируется как минус бесконечность"
    ,
      "minimum": -1e+99,
      "maximum": 1e+99
    },
    "baseline_normal_min": {
      "type": [
        "number",
        "null"
      ],
      "description": "Минимальное отклонение от значения baseline_value. Может
      быть числом или null. В случае null интерпретируется как минус бесконечность"
    ,
      "minimum": 0,
      "maximum": 1e+99
    },
    "baseline_normal_max": {
      "type": [
        "number",
        "null"
      ],
      "description": "Максимальное отклонение от значения baseline_value. Может
      быть числом или null. В случае null интерпретируется как плюс бесконечность"
    ,
      "minimum": 1e-99,
      "maximum": 1e+99
    },
    "baseline_warning_min": {
      "type": [
        "number",
        "null"
      ],
      "description": "Минимальное отклонение от значения baseline_value. Может
      быть числом или null. В случае null интерпретируется как минус бесконечность"
    ,
      "minimum": 0,
      "maximum": 1e+99
    },
    "baseline_warning_max": {
      "type": [
        "number",
        "null"
      ],
      "description": "Максимальное отклонение от значения baseline_value. Может
      быть числом или null. В случае null интерпретируется как плюс бесконечность"
    ,
      "minimum": 1e-99,
      "maximum": 1e+99
    },
    "baseline_high_min": {
      "type": [
        "number",
        "null"
      ],
      "description": "Минимальное отклонение от значения baseline_value. Может
      быть числом или null. В случае null интерпретируется как минус бесконечность"
    ,
      "minimum": 0,
      "maximum": 1e+99
    },
    "baseline_high_max": {
      "type": [
        "number",
        "null"
      ],
      "description": "Максимальное отклонение от значения baseline_value. Может
      быть числом или null. В случае null интерпретируется как плюс бесконечность"
    ,
      "minimum": 1e-99,
      "maximum": 1e+99
    },
    "baseline_critical_min": {
      "type": [
        "number",
        "null"
      ],
      "description": "Минимальное отклонение от значения baseline_value. Может
      быть числом или null. В случае null интерпретируется как минус бесконечность"
    ,
      "minimum": 0,
      "maximum": 1e+99
    },
    "baseline_critical_max": {
      "type": [
        "number",
        "null"
      ],
      "description": "Максимальное отклонение от значения baseline_value. Может
      быть числом или null. В случае null интерпретируется как плюс бесконечность"
    ,
      "minimum": 1e-99,
      "maximum": 1e+99
    },
    "baseline_wide_ranging_min": {
      "type": [
        "number",
        "null"
      ],
      "description": "Минимальное отклонение от значения baseline_value. Может
      быть числом или null. В случае null интерпретируется как минус бесконечность"
    ,
      "minimum": 0,
      "maximum": 1e+99,
    },
    "baseline_wide_ranging_max": {
      "type": [
        "number",
        "null"
      ],
      "description": "Максимальное отклонение от значения baseline_value. Может
      быть числом или null. В случае null интерпретируется как плюс бесконечность"
    ,
      "minimum": 1e-99,
      "maximum": 1e+99
    },
    "is_percent": {
      "description": "Признак того, процентов, а не абсолютного значения"
    ,
      "type": "boolean"
      что значения границ коридора переданы в виде
    },
    "threshold_timestamp": {
      "type": [
        "integer"
      ],
      "description": "Unix timestamp в секундах c 01/01/1970 в UTC (без
      timezone), обозначающий момент времени, в который значение порога, указанное в полях
      threshold или baseline вступило в силу. На оперативном дашборде всегда применяется
      ПОСЛЕДНИЙ ФАКТИЧЕСКИ полученный пакет с порогом, threshold_timestamp используется только
      для диагностики в случае сбоев. В случае если пороги не передаются в threshold_timestamp
      указывается null",
      "minimum": 0,
      "maximum": 3147483648
    }
  },
  "required": [
    "metric_value",
    "baseline_value",
    "metric_timestamp",
    "metric_actual_date",
    "version",
    "it_service_ci",
    "object_ci",
    "metric_name",
    "metric_description",
    "metric_type",
    "metric_unit",
    "metric_period_sec"
  ],
  "additionalProperties": false
}
