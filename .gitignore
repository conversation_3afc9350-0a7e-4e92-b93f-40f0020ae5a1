# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib
radar-integrator

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out
coverage.html

# Dependency directories (remove the comment below to include it)
vendor/

# Go workspace file
go.work

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Docker
.dockerignore

# Helm
charts/*.tgz

# Certificates (for security)
certs/*.crt
certs/*.key
certs/*.pem

# Environment files
.env
.env.local
.env.*.local

# Logs
*.log

# Temporary files
tmp/
temp/
